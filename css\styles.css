/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Preloader */
#preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.loader {
    text-align: center;
    color: white;
}

.logo-animation {
    animation: pulse 2s infinite;
}

.logo-animation i {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
}

.logo-animation h2 {
    font-size: 1.5rem;
    font-weight: 300;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 600;
    color: #667eea;
}

.nav-logo i {
    margin-right: 10px;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #667eea;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #667eea;
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-content {
    color: white;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.typing-text {
    border-right: 3px solid white;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { border-color: white; }
    51%, 100% { border-color: transparent; }
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    border: none;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-primary {
    background: white;
    color: #667eea;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #667eea;
    transform: translateY(-2px);
}

/* Hero Image */
.hero-image {
    position: relative;
    height: 500px;
}

.floating-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

.floating-card {
    position: absolute;
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    animation: float 3s ease-in-out infinite;
}

.floating-card i {
    font-size: 2rem;
    color: #667eea;
}

.floating-card span {
    font-weight: 600;
    color: #333;
}

.card-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.card-2 {
    top: 50%;
    right: 20%;
    animation-delay: 1s;
}

.card-3 {
    bottom: 20%;
    left: 30%;
    animation-delay: 2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    animation: bounce 2s infinite;
}

.scroll-arrow i {
    font-size: 1.5rem;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* Features Section */
.features {
    padding: 100px 0;
    background: #f8f9fa;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #666;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.feature-icon i {
    font-size: 2rem;
    color: white;
}

.feature-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.1rem;
    color: white;
    opacity: 0.9;
    margin-bottom: 2rem;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 50px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-logo i {
    margin-right: 10px;
    font-size: 1.8rem;
    color: #667eea;
}

.footer-section p {
    margin-bottom: 1.5rem;
    opacity: 0.8;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: #667eea;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.social-link:hover {
    transform: translateY(-3px);
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: #667eea;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: white;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.footer-section ul li a:hover {
    opacity: 1;
}

.footer-section ul li i {
    margin-right: 10px;
    color: #667eea;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    opacity: 0.8;
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 1000;
}

.scroll-to-top:hover {
    background: #764ba2;
    transform: translateY(-3px);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 2rem;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.close {
    position: absolute;
    right: 20px;
    top: 15px;
    font-size: 2rem;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

.subscribe-form {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.subscribe-form input {
    flex: 1;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 25px;
    outline: none;
}

.subscribe-form input:focus {
    border-color: #667eea;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .hero-image {
        height: 300px;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .subscribe-form {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}

/* Page Header Styles */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 120px 0 80px;
    color: white;
    text-align: center;
}

.header-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.header-content h1 i {
    margin-right: 15px;
    color: rgba(255, 255, 255, 0.9);
}

.header-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.breadcrumb {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    font-size: 1rem;
}

.breadcrumb a {
    color: white;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.breadcrumb a:hover {
    opacity: 1;
}

.breadcrumb span {
    opacity: 0.6;
}

/* Search and Filter Section */
.search-filter-section {
    padding: 60px 0;
    background: #f8f9fa;
}

.search-filter-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    align-items: center;
}

.search-box {
    position: relative;
    width: 100%;
    max-width: 600px;
}

.search-box input {
    width: 100%;
    padding: 15px 60px 15px 20px;
    border: 2px solid #ddd;
    border-radius: 50px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.3s ease;
}

.search-box input:focus {
    border-color: #667eea;
}

.search-box button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: #667eea;
    color: white;
    border: none;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-box button:hover {
    background: #764ba2;
}

.filter-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.filter-btn {
    padding: 10px 20px;
    border: 2px solid #667eea;
    background: transparent;
    color: #667eea;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: #667eea;
    color: white;
}

/* Notes Section */
.notes-section {
    padding: 80px 0;
}

.notes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.note-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.note-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.note-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.note-subject {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    color: white;
}

.note-subject.mathematics {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.note-subject.science {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

.note-subject.english {
    background: linear-gradient(135deg, #a8edea, #fed6e3);
    color: #333;
}

.note-subject.history {
    background: linear-gradient(135deg, #ffecd2, #fcb69f);
    color: #333;
}

.note-subject.geography {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.note-level {
    background: #f8f9fa;
    color: #666;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 500;
}

.note-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
    line-height: 1.4;
}

.note-card p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.note-meta {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    color: #888;
}

.note-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.note-actions {
    display: flex;
    gap: 1rem;
}

.note-actions .btn {
    flex: 1;
    justify-content: center;
    font-size: 0.9rem;
    padding: 10px 15px;
}

.load-more-section {
    text-align: center;
}

.load-more-btn {
    padding: 15px 30px;
    font-size: 1rem;
}

/* Back to Home */
.back-to-home {
    position: fixed;
    bottom: 30px;
    left: 30px;
    z-index: 1000;
}

.back-to-home .btn {
    background: rgba(255, 255, 255, 0.9);
    color: #667eea;
    border: 2px solid #667eea;
    backdrop-filter: blur(10px);
}

.back-to-home .btn:hover {
    background: #667eea;
    color: white;
}

/* Featured News Section */
.featured-news {
    padding: 80px 0;
    background: #f8f9fa;
}

.featured-article {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.featured-image {
    position: relative;
    height: 400px;
    overflow: hidden;
}

.featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.featured-article:hover .featured-image img {
    transform: scale(1.05);
}

.featured-badge {
    position: absolute;
    top: 20px;
    left: 20px;
    background: #ff6b6b;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.featured-content {
    padding: 2rem;
}

.article-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    align-items: center;
}

.category {
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
}

.category.education {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.date {
    color: #888;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.featured-content h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #333;
    line-height: 1.3;
}

.featured-content p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

/* News Section */
.news-section {
    padding: 80px 0;
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.news-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.news-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-card:hover .news-image img {
    transform: scale(1.1);
}

.news-category {
    position: absolute;
    top: 15px;
    left: 15px;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
}

.news-category.exam {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.news-category.scholarship {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

.news-category.tips {
    background: linear-gradient(135deg, #a8edea, #fed6e3);
    color: #333;
}

.news-category.career {
    background: linear-gradient(135deg, #ffecd2, #fcb69f);
    color: #333;
}

.news-category.university {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.news-category.technology {
    background: linear-gradient(135deg, #2c3e50, #34495e);
}

.news-content {
    padding: 1.5rem;
}

.news-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.85rem;
    color: #888;
}

.news-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.read-time {
    color: #888;
}

.news-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
    line-height: 1.4;
}

.news-card p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.read-more {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

.read-more:hover {
    color: #764ba2;
    gap: 10px;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 1rem;
    align-items: center;
}

.page-link {
    padding: 10px 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    text-decoration: none;
    color: #666;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.page-link:hover,
.page-link.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Newsletter Section */
.newsletter-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.newsletter-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.newsletter-text {
    color: white;
}

.newsletter-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 15px;
}

.newsletter-text p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    gap: 1rem;
}

.newsletter-form input {
    flex: 1;
    padding: 15px 20px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    outline: none;
}

.newsletter-form button {
    padding: 15px 30px;
    border-radius: 50px;
    white-space: nowrap;
}

/* Quick Access Section */
.quick-access {
    padding: 80px 0;
    background: #f8f9fa;
}

.quick-access-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.quick-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.quick-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.quick-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.quick-icon i {
    font-size: 2rem;
    color: white;
}

.quick-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.quick-card p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* Resources Section */
.resources-section {
    padding: 80px 0;
}

.resources-section:nth-child(even) {
    background: #f8f9fa;
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.resource-card {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.resource-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.resource-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.resource-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.resource-icon.mathematics {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.resource-icon.science {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

.resource-icon.english {
    background: linear-gradient(135deg, #a8edea, #fed6e3);
    color: #333;
}

.resource-icon i {
    font-size: 1.5rem;
}

.resource-info h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.resource-info p {
    color: #666;
    font-size: 0.9rem;
}

.resource-details {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    color: #888;
}

.resource-details span {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Papers Section */
.papers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.paper-year-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.paper-year-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.year-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.year-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
}

.paper-count {
    background: #667eea;
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.paper-subjects {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.subject-tag {
    background: #f8f9fa;
    color: #666;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Links Section */
.links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.link-category {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.link-category h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.link-category h3 i {
    color: #667eea;
}

.links-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.external-link {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    background: #f8f9fa;
    border-radius: 10px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.external-link:hover {
    background: #667eea;
    color: white;
    transform: translateX(5px);
}

.external-link i {
    color: #667eea;
    transition: color 0.3s ease;
}

.external-link:hover i {
    color: white;
}

/* Video Section */
.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.video-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.video-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.video-card:hover .video-thumbnail img {
    transform: scale(1.1);
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #667eea;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.video-card:hover .play-button {
    background: #667eea;
    color: white;
    transform: translate(-50%, -50%) scale(1.1);
}

.video-info {
    padding: 1.5rem;
}

.video-info h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.video-info p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* Mission Section */
.mission-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.mission-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.mission-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: #333;
}

.mission-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #666;
    margin-bottom: 1.5rem;
}

.mission-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

.mission-image {
    position: relative;
}

.parallax-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.parallax-container img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.3));
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 60px;
    height: 60px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 40px;
    height: 40px;
    top: 30%;
    right: 30%;
    animation-delay: 1s;
}

/* Values Section */
.values-section {
    padding: 100px 0;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.value-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.value-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.value-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.value-icon i {
    font-size: 2rem;
    color: white;
}

.value-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.value-card p {
    color: #666;
    line-height: 1.6;
}

/* Team Section */
.team-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.team-member {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.team-member:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.member-image {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.team-member:hover .member-image img {
    transform: scale(1.1);
}

.member-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(102, 126, 234, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.team-member:hover .member-overlay {
    opacity: 1;
}

.member-overlay .social-links {
    display: flex;
    gap: 1rem;
}

.member-overlay .social-links a {
    width: 50px;
    height: 50px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #667eea;
    text-decoration: none;
    transition: all 0.3s ease;
}

.member-overlay .social-links a:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.member-info {
    padding: 2rem;
    text-align: center;
}

.member-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.member-role {
    color: #667eea;
    font-weight: 600;
    margin-bottom: 1rem;
}

.member-bio {
    color: #666;
    line-height: 1.6;
    font-size: 0.9rem;
}

/* Story Section */
.story-section {
    padding: 100px 0;
}

.story-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.story-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: #333;
}

.story-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #666;
    margin-bottom: 1.5rem;
}

.timeline {
    position: relative;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #667eea;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
    padding-left: 80px;
}

.timeline-year {
    position: absolute;
    left: 0;
    top: 0;
    width: 60px;
    height: 60px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.timeline-content {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.timeline-content h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.timeline-content p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Contact Section */
.contact-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-form-container h2,
.contact-info-container h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #333;
}

.contact-form-container p,
.contact-info-container p {
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Contact Form */
.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Contact Info */
.contact-info {
    margin-bottom: 2rem;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
}

.info-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.info-content h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.info-content p {
    color: #666;
    margin: 0;
    line-height: 1.5;
}

/* Social Section */
.social-section {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.social-section h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.social-section p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.social-section .social-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.social-section .social-link {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px 15px;
    background: #f8f9fa;
    border-radius: 10px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.social-section .social-link:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.social-section .social-link.instagram:hover {
    background: linear-gradient(135deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
    color: white;
}

.social-section .social-link.facebook:hover {
    background: #1877f2;
    color: white;
}

.social-section .social-link.twitter:hover {
    background: #1da1f2;
    color: white;
}

.social-section .social-link.youtube:hover {
    background: #ff0000;
    color: white;
}

.social-section .social-link i {
    font-size: 1.2rem;
    width: 20px;
}

/* Instagram Section */
.instagram-section {
    padding: 100px 0;
}

.instagram-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.instagram-post {
    position: relative;
    aspect-ratio: 1;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
}

.instagram-post img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.instagram-post:hover img {
    transform: scale(1.1);
}

.post-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.instagram-post:hover .post-overlay {
    opacity: 1;
}

.post-stats {
    display: flex;
    gap: 1rem;
    color: white;
    font-weight: 600;
}

.post-stats span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.instagram-cta {
    text-align: center;
}

/* FAQ Section */
.faq-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: white;
    border-radius: 10px;
    margin-bottom: 1rem;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.3s ease;
}

.faq-question:hover {
    background: #f8f9fa;
}

.faq-question h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.faq-question i {
    color: #667eea;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 0 1.5rem 1.5rem;
    max-height: 200px;
}

.faq-answer p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

/* Responsive Design for New Pages */
@media (max-width: 768px) {
    .page-header {
        padding: 100px 0 60px;
    }

    .header-content h1 {
        font-size: 2.5rem;
    }

    .breadcrumb {
        font-size: 0.9rem;
    }

    /* Search and Filter */
    .search-filter-container {
        gap: 1.5rem;
    }

    .filter-buttons {
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }

    /* Notes Grid */
    .notes-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .note-actions {
        flex-direction: column;
    }

    /* News Grid */
    .featured-article {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .featured-image {
        height: 250px;
    }

    .featured-content {
        padding: 1.5rem;
    }

    .featured-content h2 {
        font-size: 1.5rem;
    }

    .news-grid {
        grid-template-columns: 1fr;
    }

    .newsletter-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .newsletter-text h2 {
        font-size: 2rem;
    }

    .newsletter-form {
        flex-direction: column;
    }

    /* Resources */
    .quick-access-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .resources-grid {
        grid-template-columns: 1fr;
    }

    .papers-grid {
        grid-template-columns: 1fr;
    }

    .links-grid {
        grid-template-columns: 1fr;
    }

    .video-grid {
        grid-template-columns: 1fr;
    }

    /* About Page */
    .mission-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .mission-text h2 {
        font-size: 2rem;
    }

    .mission-stats {
        gap: 1rem;
        justify-content: center;
    }

    .stat-number {
        font-size: 2rem;
    }

    .values-grid {
        grid-template-columns: 1fr;
    }

    .team-grid {
        grid-template-columns: 1fr;
    }

    .story-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .story-text h2 {
        font-size: 2rem;
    }

    .timeline::before {
        left: 20px;
    }

    .timeline-item {
        padding-left: 60px;
    }

    .timeline-year {
        width: 40px;
        height: 40px;
        font-size: 0.8rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    /* Contact Page */
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .instagram-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .back-to-home {
        bottom: 20px;
        left: 20px;
    }

    .back-to-home .btn {
        padding: 10px 15px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .header-content h1 {
        font-size: 2rem;
    }

    .mission-text h2,
    .story-text h2,
    .newsletter-text h2 {
        font-size: 1.8rem;
    }

    .featured-content h2 {
        font-size: 1.3rem;
    }

    .mission-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .timeline-item {
        padding-left: 50px;
    }

    .timeline-year {
        width: 35px;
        height: 35px;
        font-size: 0.7rem;
    }

    .timeline::before {
        left: 17px;
    }

    .instagram-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .social-section .social-links {
        gap: 0.5rem;
    }

    .social-section .social-link {
        padding: 10px 12px;
        font-size: 0.9rem;
    }
}
